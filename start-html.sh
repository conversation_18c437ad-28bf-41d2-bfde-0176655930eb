#!/bin/bash

echo "🎵 Starting NCast Podcast App..."
echo "📱 Opening HTML page in browser..."

# Open the HTML file in the default browser
if command -v open &> /dev/null; then
    # macOS
    open index.html
elif command -v xdg-open &> /dev/null; then
    # Linux
    xdg-open index.html
elif command -v start &> /dev/null; then
    # Windows (Git Bash)
    start index.html
else
    echo "🌐 Please open index.html in your browser manually"
fi

echo "✅ NCast Podcast App started successfully!"
echo "🎧 You can now view the podcast app in your browser."
