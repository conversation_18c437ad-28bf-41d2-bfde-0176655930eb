<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NCast - Explore</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #ffffff;
            color: #1f1f1f;
            width: 428px;
            height: 926px;
            margin: 0 auto;
            position: relative;
            overflow: hidden;
        }

        .container {
            padding: 48px 32px 0;
            height: 100%;
            position: relative;
        }

        /* Header */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 56px;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .logo-icon {
            width: 50px;
            height: 46px;
        }

        .logo-text {
            font-size: 24px;
            font-weight: bold;
            color: #4c0099;
        }

        .notification-container {
            position: relative;
        }

        .notification-btn {
            width: 48px;
            height: 48px;
            background: rgba(31, 31, 31, 0.1);
            border-radius: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            cursor: pointer;
        }

        .bell-icon {
            width: 21px;
            height: 21px;
        }

        .notification-badge {
            position: absolute;
            top: 2px;
            right: -2px;
            width: 12px;
            height: 12px;
            background: #ff5757;
            border-radius: 6px;
        }

        /* Title */
        .title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 24px;
        }

        /* Hero Images */
        .hero-section {
            display: flex;
            gap: 16px;
            margin-bottom: 32px;
        }

        .hero-image {
            width: 200px;
            height: 200px;
            border-radius: 24px;
            object-fit: cover;
        }

        /* Category Tags */
        .categories {
            display: flex;
            gap: 12px;
            margin-bottom: 32px;
        }

        .category-tag {
            height: 56px;
            padding: 0 25px;
            border-radius: 28px;
            display: flex;
            align-items: center;
            font-size: 16px;
            font-weight: 500;
            border: none;
            cursor: pointer;
        }

        .category-tag.active {
            background: #ff5757;
            color: #ffffff;
        }

        .category-tag.inactive {
            background: rgba(255, 87, 87, 0.1);
            color: #1f1f1f;
        }

        /* Podcast List */
        .podcast-list {
            display: flex;
            flex-direction: column;
            gap: 24px;
            margin-bottom: 100px;
        }

        .podcast-item {
            display: flex;
            gap: 16px;
            align-items: center;
        }

        .podcast-cover {
            width: 108px;
            height: 96px;
            border-radius: 16px;
            object-fit: cover;
        }

        .podcast-info {
            flex: 1;
        }

        .podcast-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .podcast-author {
            font-size: 14px;
            color: rgba(31, 31, 31, 0.7);
            margin-bottom: 8px;
        }

        .podcast-duration {
            font-size: 14px;
            color: rgba(31, 31, 31, 0.7);
        }

        .play-btn {
            width: 48px;
            height: 48px;
            background: rgba(76, 0, 153, 0.1);
            border-radius: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            cursor: pointer;
        }

        .play-icon {
            width: 18px;
            height: 18px;
        }

        /* Bottom Navigation */
        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 158px;
            background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 100%);
            display: flex;
            align-items: flex-end;
            padding: 32px;
        }

        .nav-container {
            width: 100%;
            height: 72px;
            background: rgba(76, 0, 153, 0.1);
            border-radius: 48px;
            display: flex;
            align-items: center;
            justify-content: space-around;
            position: relative;
        }

        .nav-item {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0.5;
            cursor: pointer;
        }

        .nav-item.active {
            opacity: 1;
        }

        .nav-item.active::after {
            content: '';
            position: absolute;
            bottom: 20px;
            width: 5px;
            height: 5px;
            background: #4c0099;
            border-radius: 50%;
        }

        .nav-icon {
            width: 32px;
            height: 32px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">
                <img src="images/logo-icon.png" alt="NCast Logo" class="logo-icon">
                <span class="logo-text">NCAST</span>
            </div>
            <div class="notification-container">
                <button class="notification-btn">
                    <img src="images/bell-icon.png" alt="Notifications" class="bell-icon">
                </button>
                <div class="notification-badge"></div>
            </div>
        </div>

        <!-- Title -->
        <h1 class="title">Today's Top 5 Podcasts</h1>

        <!-- Hero Images -->
        <div class="hero-section">
            <img src="images/hero-image-1.png" alt="Featured Podcast 1" class="hero-image">
            <img src="images/hero-image-2.png" alt="Featured Podcast 2" class="hero-image">
        </div>

        <!-- Category Tags -->
        <div class="categories">
            <button class="category-tag active">Motivation</button>
            <button class="category-tag inactive">Lifestyle</button>
            <button class="category-tag inactive">Business</button>
        </div>

        <!-- Podcast List -->
        <div class="podcast-list">
            <div class="podcast-item">
                <img src="images/podcast-image-2.png" alt="Enjoy It!" class="podcast-cover">
                <div class="podcast-info">
                    <div class="podcast-title">Enjoy It!</div>
                    <div class="podcast-author">Socially Buzzed</div>
                    <div class="podcast-duration">22 min</div>
                </div>
                <button class="play-btn">
                    <img src="images/play-icon-2.png" alt="Play" class="play-icon">
                </button>
            </div>

            <div class="podcast-item">
                <img src="images/podcast-image-3.png" alt="Grow with Us" class="podcast-cover">
                <div class="podcast-info">
                    <div class="podcast-title">Grow with Us</div>
                    <div class="podcast-author">Business</div>
                    <div class="podcast-duration">12 min</div>
                </div>
                <button class="play-btn">
                    <img src="images/play-icon-3.png" alt="Play" class="play-icon">
                </button>
            </div>

            <div class="podcast-item">
                <img src="images/podcast-image-4.png" alt="Crack the Interview" class="podcast-cover">
                <div class="podcast-info">
                    <div class="podcast-title">Crack the Interview</div>
                    <div class="podcast-author">Educational</div>
                    <div class="podcast-duration">30 min</div>
                </div>
                <button class="play-btn">
                    <img src="images/play-icon-4.png" alt="Play" class="play-icon">
                </button>
            </div>

            <div class="podcast-item">
                <img src="images/podcast-image-1.png" alt="Mindfulness" class="podcast-cover">
                <div class="podcast-info">
                    <div class="podcast-title">Mindfulness</div>
                    <div class="podcast-author">Motivational</div>
                    <div class="podcast-duration">15 min</div>
                </div>
                <button class="play-btn">
                    <img src="images/play-icon-1.png" alt="Play" class="play-icon">
                </button>
            </div>
        </div>
    </div>

    <!-- Bottom Navigation -->
    <div class="bottom-nav">
        <div class="nav-container">
            <div class="nav-item">
                <img src="images/headphones-icon.png" alt="Listen" class="nav-icon">
            </div>
            <div class="nav-item active">
                <img src="images/compass-icon.png" alt="Explore" class="nav-icon">
            </div>
            <div class="nav-item">
                <img src="images/heart-icon.png" alt="Favorites" class="nav-icon">
            </div>
            <div class="nav-item">
                <img src="images/profile-icon.png" alt="Profile" class="nav-icon">
            </div>
        </div>
    </div>
</body>
</html>
