* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Public Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #FFFFFF;
    color: #1F1F1F;
    overflow-x: hidden;
}

.container {
    max-width: 428px;
    margin: 0 auto;
    min-height: 100vh;
    background: #FFFFFF;
    position: relative;
}

.container::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 158px;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.9) 83.26%);
    pointer-events: none;
}

/* Header */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 32px;
    position: relative;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo-icon {
    width: 50px;
    height: 46px;
}

.logo-text {
    width: 157px;
    height: 25px;
}

.notification-wrapper {
    position: relative;
}

.notification-icon {
    width: 48px;
    height: 48px;
    background: rgba(31, 31, 31, 0.1);
    border-radius: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.bell-icon {
    width: 21px;
    height: 21px;
    position: relative;
}

.bell-main, .bell-dot {
    position: absolute;
    top: 0;
    left: 0;
}

.notification-badge {
    position: absolute;
    top: 2px;
    right: 2px;
    width: 12px;
    height: 12px;
    background: #FF5757;
    border-radius: 6px;
}

/* Main Content */
.main-content {
    padding: 0 32px;
    padding-bottom: 100px;
}

.page-title {
    font-weight: 700;
    font-size: 20px;
    line-height: 1.175;
    color: #1F1F1F;
    margin-bottom: 24px;
}

/* Category Tags */
.category-tags {
    display: flex;
    gap: 12px;
    margin-bottom: 32px;
}

.tag {
    height: 56px;
    padding: 18px 25px;
    border-radius: 28px;
    font-weight: 500;
    font-size: 16px;
    line-height: 1.175;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tag-active {
    background: #FF5757;
    color: #FFFFFF;
    font-weight: 700;
}

.tag:not(.tag-active) {
    background: rgba(255, 87, 87, 0.1);
    color: #1F1F1F;
}

/* Podcast List */
.podcast-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 32px;
}

.podcast-item {
    display: flex;
    align-items: center;
    gap: 16px;
    height: 96px;
}

.podcast-image {
    position: relative;
    width: 108px;
    height: 96px;
    border-radius: 16px;
    overflow: hidden;
}

.podcast-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.play-button {
    position: absolute;
    right: 15px;
    bottom: 15px;
    width: 48px;
    height: 48px;
    background: rgba(76, 0, 153, 0.1);
    border-radius: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.play-button img {
    width: 18px;
    height: 18px;
}

.podcast-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.podcast-title {
    font-weight: 700;
    font-size: 16px;
    line-height: 1.175;
    color: #1F1F1F;
}

.podcast-category {
    font-weight: 400;
    font-size: 14px;
    line-height: 1.175;
    color: rgba(31, 31, 31, 0.7);
}

.podcast-duration {
    font-weight: 400;
    font-size: 14px;
    line-height: 1.175;
    color: rgba(31, 31, 31, 0.7);
}

/* Avatar Section */
.avatar-section {
    display: flex;
    gap: 20px;
    margin-bottom: 40px;
    justify-content: center;
    position: relative;
    height: 200px;
}

.avatar-large {
    width: 200px;
    height: 200px;
    border-radius: 24px;
    overflow: hidden;
    background: #D9D9D9;
    position: relative;
}

.avatar-small {
    width: 200px;
    height: 200px;
    border-radius: 24px;
    overflow: hidden;
    background: #FFFFFF;
    position: absolute;
    right: -100px;
    top: 0;
    z-index: 2;
}

.avatar-large img,
.avatar-small img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Bottom Navigation */
.bottom-nav {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 364px;
    height: 72px;
    background: rgba(76, 0, 153, 0.1);
    backdrop-filter: blur(32px);
    border-radius: 48px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 40px;
}

.nav-item {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    position: relative;
}

.nav-icon {
    width: 26px;
    height: 26px;
    opacity: 0.5;
    transition: opacity 0.3s ease;
}

.nav-active .nav-icon {
    opacity: 1;
}

.nav-active::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 5px;
    height: 5px;
    background: #4C0099;
    border-radius: 50%;
}
