<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NCAST - Today's Top 5 Podcasts</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8f9fa;
            color: #333;
        }

        .container {
            max-width: 375px;
            margin: 0 auto;
            background-color: white;
            min-height: 100vh;
            position: relative;
            padding-bottom: 80px;
        }

        /* Header */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 20px 10px;
            background-color: white;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #6B46C1, #9333EA);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 16px;
        }

        .logo-text {
            font-size: 24px;
            font-weight: bold;
            color: #6B46C1;
        }

        .notification-btn {
            width: 40px;
            height: 40px;
            background-color: #f3f4f6;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            border: none;
            cursor: pointer;
        }

        .notification-btn::before {
            content: '🔔';
            font-size: 18px;
        }

        .notification-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 8px;
            height: 8px;
            background-color: #ef4444;
            border-radius: 50%;
        }

        /* Main Content */
        .main-content {
            padding: 0 20px;
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #1f2937;
        }

        /* Featured Podcasts */
        .featured-podcasts {
            display: flex;
            gap: 12px;
            margin-bottom: 24px;
        }

        .podcast-card {
            flex: 1;
            height: 160px;
            border-radius: 12px;
            padding: 16px;
            color: white;
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .podcast-card.talk-show {
            background: linear-gradient(135deg, #ec4899, #f59e0b);
        }

        .podcast-card.writing {
            background: linear-gradient(135deg, #f59e0b, #ea580c);
        }

        .podcast-title {
            font-size: 16px;
            font-weight: bold;
            line-height: 1.2;
        }

        .podcast-subtitle {
            font-size: 12px;
            opacity: 0.9;
            margin-top: 4px;
        }

        .podcast-host {
            font-size: 11px;
            opacity: 0.8;
        }

        /* Categories */
        .categories {
            display: flex;
            gap: 12px;
            margin-bottom: 24px;
        }

        .category-tag {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            border: none;
            cursor: pointer;
        }

        .category-tag.active {
            background-color: #ef4444;
            color: white;
        }

        .category-tag.inactive {
            background-color: #f3f4f6;
            color: #6b7280;
        }

        /* Podcast List */
        .podcast-list {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .podcast-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 0;
        }

        .podcast-cover {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 12px;
            text-align: center;
        }

        .podcast-cover.enjoy {
            background: linear-gradient(135deg, #06b6d4, #0891b2);
        }

        .podcast-cover.grow {
            background: linear-gradient(135deg, #f59e0b, #d97706);
        }

        .podcast-cover.crack {
            background: linear-gradient(135deg, #ec4899, #be185d);
        }

        .podcast-info {
            flex: 1;
        }

        .podcast-name {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 2px;
        }

        .podcast-meta {
            font-size: 14px;
            color: #6b7280;
        }

        .play-btn {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
            border-radius: 50%;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }

        .play-btn::before {
            content: '▶';
            color: white;
            font-size: 14px;
            margin-left: 2px;
        }

        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 375px;
            max-width: 100%;
            background: rgba(139, 92, 246, 0.1);
            backdrop-filter: blur(10px);
            padding: 16px 20px;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-radius: 20px 20px 0 0;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            background: none;
            border: none;
            cursor: pointer;
            padding: 8px;
            border-radius: 12px;
        }

        .nav-item.active {
            background-color: rgba(139, 92, 246, 0.2);
        }

        .nav-icon {
            width: 24px;
            height: 24px;
            background-color: #8b5cf6;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }

        .nav-item.active .nav-icon {
            background-color: #7c3aed;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="logo">
                <div class="logo-icon">N</div>
                <div class="logo-text">NCAST</div>
            </div>
            <button class="notification-btn">
                <div class="notification-badge"></div>
            </button>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <h1 class="section-title">Today's Top 5 Podcasts</h1>

            <!-- Featured Podcasts -->
            <div class="featured-podcasts">
                <div class="podcast-card talk-show">
                    <div>
                        <div class="podcast-title">TALK<br>SHOW<br>Podcast.</div>
                        <div class="podcast-subtitle">Conversations</div>
                        <div class="podcast-host">with Lisa Maclear.</div>
                    </div>
                </div>
                <div class="podcast-card writing">
                    <div>
                        <div class="podcast-title">WRITING<br>PODCAST<br>COVER.</div>
                        <div class="podcast-host">with Lucy Ross</div>
                    </div>
                </div>
            </div>

            <!-- Categories -->
            <div class="categories">
                <button class="category-tag active">Motivation</button>
                <button class="category-tag inactive">Lifestyle</button>
                <button class="category-tag inactive">Business</button>
            </div>

            <!-- Podcast List -->
            <div class="podcast-list">
                <div class="podcast-item">
                    <div class="podcast-cover enjoy">POD<br>CAST</div>
                    <div class="podcast-info">
                        <div class="podcast-name">Enjoy It!</div>
                        <div class="podcast-meta">Socially Buzzed<br>22 min</div>
                    </div>
                    <button class="play-btn"></button>
                </div>

                <div class="podcast-item">
                    <div class="podcast-cover grow">Talk Sessions<br>Podcast</div>
                    <div class="podcast-info">
                        <div class="podcast-name">Grow with Us</div>
                        <div class="podcast-meta">Business<br>12 min</div>
                    </div>
                    <button class="play-btn"></button>
                </div>

                <div class="podcast-item">
                    <div class="podcast-cover crack">THE QUESTION</div>
                    <div class="podcast-info">
                        <div class="podcast-name">Crack the Interview</div>
                        <div class="podcast-meta">Educational<br>30 min</div>
                    </div>
                    <button class="play-btn"></button>
                </div>
            </div>
        </main>

        <!-- Bottom Navigation -->
        <nav class="bottom-nav">
            <button class="nav-item">
                <div class="nav-icon">🎧</div>
            </button>
            <button class="nav-item active">
                <div class="nav-icon">🔍</div>
            </button>
            <button class="nav-item">
                <div class="nav-icon">❤</div>
            </button>
            <button class="nav-item">
                <div class="nav-icon">👤</div>
            </button>
        </nav>
    </div>
</body>
</html>
