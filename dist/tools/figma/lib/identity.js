/**
 * Identity and validation utility functions
 * Copied from Figma-Context-MCP
 */
/**
 * Check if a value exists and is not null/undefined
 */
export function hasValue(key, obj, validator) {
    const value = obj[key];
    if (value === null || value === undefined) {
        return false;
    }
    return validator ? validator(value) : true;
}
/**
 * Check if a value is truthy
 */
export function isTruthy(value) {
    return Boolean(value);
}
/**
 * Check if value is a valid rectangle corner radii array
 */
export function isRectangleCornerRadii(value) {
    return Array.isArray(value) && value.length === 4 && value.every(v => typeof v === 'number');
}
/**
 * Check if an element is visible
 */
export function isVisible(element) {
    return element.visible ?? true;
}
//# sourceMappingURL=identity.js.map