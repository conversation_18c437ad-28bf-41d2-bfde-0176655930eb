/**
 * Sanitization utilities for Figma components
 * Copied from Figma-Context-MCP
 */
import type { Component, ComponentSet } from "@figma/rest-api-spec";
import type { SimplifiedComponentDefinition, SimplifiedComponentSetDefinition } from "./types.js";
/**
 * Sanitize components data
 */
export declare function sanitizeComponents(components: Record<string, Component>): Record<string, SimplifiedComponentDefinition>;
/**
 * Sanitize component sets data
 */
export declare function sanitizeComponentSets(componentSets: Record<string, ComponentSet>): Record<string, SimplifiedComponentSetDefinition>;
//# sourceMappingURL=sanitization.d.ts.map