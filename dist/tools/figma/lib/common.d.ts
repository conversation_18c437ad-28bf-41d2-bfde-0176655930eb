/**
 * Common utility functions
 * Copied and adapted from Figma-Context-MCP
 */
import type { Paint } from "@figma/rest-api-spec";
import type { SimplifiedFill, ColorValue, StyleId } from "./types.js";
/**
 * Remove empty keys from an object
 */
export declare function removeEmptyKeys<T extends Record<string, any>>(obj: T): T;
/**
 * Generate a unique variable ID
 */
export declare function generateVarId(prefix: string): StyleId;
/**
 * Convert Figma color to hex and opacity
 */
export declare function convertColor(color: {
    r: number;
    g: number;
    b: number;
}, opacity?: number): ColorValue;
/**
 * Format RGBA color string
 */
export declare function formatRGBAColor(color: {
    r: number;
    g: number;
    b: number;
}, opacity: number): string;
/**
 * Convert a Figma paint (solid, image, gradient) to a SimplifiedFill
 */
export declare function parsePaint(raw: Paint): SimplifiedFill;
/**
 * Check if an element is visible
 */
export declare function isVisible(element: {
    visible?: boolean;
}): boolean;
//# sourceMappingURL=common.d.ts.map