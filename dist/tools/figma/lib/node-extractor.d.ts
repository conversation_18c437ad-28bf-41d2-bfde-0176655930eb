/**
 * Node extraction utilities for converting hierarchical nodes to downloadable image arrays
 */
import type { SimplifiedDesign } from "./types.js";
export interface DownloadableNode {
    nodeId: string;
    fileName: string;
    imageRef?: string;
    nodeType: string;
    nodeName: string;
}
/**
 * Extract downloadable nodes from a simplified design
 */
export declare function extractDownloadableNodes(design: SimplifiedDesign): DownloadableNode[];
//# sourceMappingURL=node-extractor.d.ts.map