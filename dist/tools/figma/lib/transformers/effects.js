/**
 * Effects transformation utilities
 * Simplified version from Figma-Context-MCP
 */
import { convertColor } from "../common.js";
/**
 * Build simplified effects information from Figma node
 */
export function buildSimplifiedEffects(node) {
    const effects = {};
    if (node.effects && Array.isArray(node.effects) && node.effects.length > 0) {
        const shadows = [];
        const blurs = [];
        for (const effect of node.effects) {
            if (!effect.visible)
                continue;
            if (effect.type === "DROP_SHADOW" || effect.type === "INNER_SHADOW") {
                const { hex, opacity } = convertColor(effect.color, effect.color.a || 1);
                shadows.push({
                    type: effect.type,
                    color: opacity === 1 ? hex : `rgba(${Math.round(effect.color.r * 255)}, ${Math.round(effect.color.g * 255)}, ${Math.round(effect.color.b * 255)}, ${opacity})`,
                    offset: {
                        x: effect.offset?.x || 0,
                        y: effect.offset?.y || 0
                    },
                    radius: effect.radius || 0,
                    spread: effect.spread || 0
                });
            }
            else if (effect.type === "LAYER_BLUR" || effect.type === "BACKGROUND_BLUR") {
                blurs.push({
                    type: effect.type,
                    radius: effect.radius || 0
                });
            }
        }
        if (shadows.length > 0) {
            effects.shadows = shadows;
        }
        if (blurs.length > 0) {
            effects.blurs = blurs;
        }
    }
    return effects;
}
//# sourceMappingURL=effects.js.map