/**
 * Layout transformation utilities
 * Simplified version from Figma-Context-MCP
 */
/**
 * Build simplified layout information from Figma node
 */
export function buildSimplifiedLayout(node, parent) {
    const layout = {};
    // Basic positioning
    if (node.x !== undefined && node.y !== undefined) {
        layout.position = "absolute";
        layout.left = `${node.x}px`;
        layout.top = `${node.y}px`;
    }
    // Dimensions
    if (node.width !== undefined && node.height !== undefined) {
        layout.width = `${node.width}px`;
        layout.height = `${node.height}px`;
    }
    // Layout mode (Flexbox)
    if (node.layoutMode) {
        if (node.layoutMode === "HORIZONTAL") {
            layout.display = "flex";
            layout.flexDirection = "row";
        }
        else if (node.layoutMode === "VERTICAL") {
            layout.display = "flex";
            layout.flexDirection = "column";
        }
    }
    // Alignment
    if (node.primaryAxisAlignItems) {
        const alignMap = {
            "MIN": "flex-start",
            "CENTER": "center",
            "MAX": "flex-end",
            "SPACE_BETWEEN": "space-between"
        };
        layout.justifyContent = alignMap[node.primaryAxisAlignItems] || node.primaryAxisAlignItems;
    }
    if (node.counterAxisAlignItems) {
        const alignMap = {
            "MIN": "flex-start",
            "CENTER": "center",
            "MAX": "flex-end"
        };
        layout.alignItems = alignMap[node.counterAxisAlignItems] || node.counterAxisAlignItems;
    }
    // Padding
    if (node.paddingLeft || node.paddingRight || node.paddingTop || node.paddingBottom) {
        const paddings = [
            node.paddingTop || 0,
            node.paddingRight || 0,
            node.paddingBottom || 0,
            node.paddingLeft || 0
        ];
        layout.padding = `${paddings.join("px ")}px`;
    }
    // Gap
    if (node.itemSpacing) {
        layout.gap = `${node.itemSpacing}px`;
    }
    return layout;
}
//# sourceMappingURL=layout.js.map