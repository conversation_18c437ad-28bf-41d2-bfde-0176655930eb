/**
 * Identity and validation utility functions
 * Copied from Figma-Context-MCP
 */
/**
 * Check if a value exists and is not null/undefined
 */
export declare function hasValue<T, K extends keyof T>(key: K, obj: T, validator?: (value: T[K]) => boolean): obj is T & Required<Pick<T, K>>;
/**
 * Check if a value is truthy
 */
export declare function isTruthy<T>(value: T): value is NonNullable<T>;
/**
 * Check if value is a valid rectangle corner radii array
 */
export declare function isRectangleCornerRadii(value: any): value is number[];
/**
 * Check if an element is visible
 */
export declare function isVisible(element: {
    visible?: boolean;
}): boolean;
//# sourceMappingURL=identity.d.ts.map