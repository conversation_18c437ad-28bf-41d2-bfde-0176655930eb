/**
 * Sanitization utilities for Figma components
 * Copied from Figma-Context-MCP
 */
/**
 * Sanitize components data
 */
export function sanitizeComponents(components) {
    const sanitized = {};
    for (const [id, component] of Object.entries(components)) {
        sanitized[id] = {
            id: component.key,
            name: component.name,
            description: component.description || undefined,
        };
    }
    return sanitized;
}
/**
 * Sanitize component sets data
 */
export function sanitizeComponentSets(componentSets) {
    const sanitized = {};
    for (const [id, componentSet] of Object.entries(componentSets)) {
        sanitized[id] = {
            id: componentSet.key,
            name: componentSet.name,
            description: componentSet.description || undefined,
        };
    }
    return sanitized;
}
//# sourceMappingURL=sanitization.js.map