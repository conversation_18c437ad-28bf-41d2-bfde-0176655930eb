<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NCast - Explore</title>
    <link href="https://fonts.googleapis.com/css2?family=Public+Sans:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Public Sans', sans-serif;
            background-color: #FFFFFF;
            color: #1F1F1F;
            padding: 20px;
            max-width: 428px;
            margin: 0 auto;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 32px;
        }

        .logo-container {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .logo-icon {
            width: 50px;
            height: 46px;
            background: linear-gradient(135deg, #4C0099, #7B2CBF);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 18px;
        }

        .logo-text {
            font-size: 24px;
            font-weight: 700;
            color: #4C0099;
        }

        .notification-container {
            position: relative;
        }

        .notification-btn {
            width: 48px;
            height: 48px;
            background: rgba(31, 31, 31, 0.1);
            border: none;
            border-radius: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }

        .notification-badge {
            position: absolute;
            top: 2px;
            right: 2px;
            width: 12px;
            height: 12px;
            background: #FF5757;
            border-radius: 6px;
        }

        .section-title {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 24px;
        }

        .categories {
            display: flex;
            gap: 12px;
            margin-bottom: 32px;
            overflow-x: auto;
        }

        .category-btn {
            padding: 18px 25px;
            border-radius: 28px;
            border: none;
            font-size: 16px;
            font-weight: 500;
            white-space: nowrap;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .category-btn.active {
            background: #4C0099;
            color: white;
            font-weight: 700;
        }

        .category-btn:not(.active) {
            background: rgba(76, 0, 153, 0.1);
            color: #1F1F1F;
        }

        .hero-section {
            display: flex;
            gap: 16px;
            margin-bottom: 32px;
            overflow-x: auto;
        }

        .hero-card {
            min-width: 200px;
            height: 200px;
            border-radius: 24px;
            background: #D9D9D9;
            overflow: hidden;
            position: relative;
        }

        .hero-card img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .podcast-list {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .podcast-item {
            display: flex;
            align-items: center;
            gap: 16px;
            padding: 0;
        }

        .podcast-image {
            width: 108px;
            height: 96px;
            border-radius: 16px;
            overflow: hidden;
            flex-shrink: 0;
            position: relative;
        }

        .podcast-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .play-btn {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            width: 48px;
            height: 48px;
            background: rgba(76, 0, 153, 0.1);
            border: none;
            border-radius: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }

        .play-icon {
            width: 18px;
            height: 18px;
            border-left: 12px solid #4C0099;
            border-top: 9px solid transparent;
            border-bottom: 9px solid transparent;
            margin-left: 3px;
        }

        .podcast-info {
            flex: 1;
        }

        .podcast-title {
            font-size: 16px;
            font-weight: 700;
            margin-bottom: 8px;
            line-height: 1.2;
        }

        .podcast-category {
            font-size: 14px;
            color: rgba(31, 31, 31, 0.7);
            margin-bottom: 8px;
        }

        .podcast-duration {
            font-size: 14px;
            color: rgba(31, 31, 31, 0.7);
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 428px;
            height: 158px;
            background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.9) 100%);
            pointer-events: none;
        }

        .nav-bar {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            width: 364px;
            height: 72px;
            background: rgba(76, 0, 153, 0.1);
            backdrop-filter: blur(32px);
            border-radius: 48px;
            display: flex;
            align-items: center;
            justify-content: space-around;
            pointer-events: all;
        }

        .nav-item {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            opacity: 0.5;
            transition: opacity 0.3s ease;
        }

        .nav-item.active {
            opacity: 1;
        }

        .nav-indicator {
            position: absolute;
            bottom: 57px;
            left: 50%;
            transform: translateX(-50%);
            width: 5px;
            height: 5px;
            background: #4C0099;
            border-radius: 50%;
        }

        .bell-icon {
            width: 21px;
            height: 21px;
            fill: #1F1F1F;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo-container">
            <div class="logo-icon">N</div>
            <div class="logo-text">NCAST</div>
        </div>
        <div class="notification-container">
            <button class="notification-btn">
                <svg class="bell-icon" viewBox="0 0 21 21">
                    <path d="M10.5 2C7.5 2 5 4.5 5 7.5V12L3 14V15H18V14L16 12V7.5C16 4.5 13.5 2 10.5 2ZM12 16H9C9 17.1 9.9 18 11 18S13 17.1 13 16H12Z"/>
                </svg>
            </button>
            <div class="notification-badge"></div>
        </div>
    </div>

    <h2 class="section-title">Today's Top 5 Podcasts</h2>

    <div class="categories">
        <button class="category-btn active">Motivation</button>
        <button class="category-btn">Lifestyle</button>
        <button class="category-btn">Business</button>
    </div>

    <div class="hero-section">
        <div class="hero-card">
            <img src="images/hero1.png" alt="Hero podcast">
        </div>
        <div class="hero-card">
            <img src="images/hero2.png" alt="Hero podcast">
        </div>
    </div>

    <div class="podcast-list">
        <div class="podcast-item">
            <div class="podcast-image">
                <img src="images/podcast1.png" alt="Mindfulness podcast">
                <button class="play-btn">
                    <div class="play-icon"></div>
                </button>
            </div>
            <div class="podcast-info">
                <div class="podcast-title">Mindfulness</div>
                <div class="podcast-category">Motivational</div>
                <div class="podcast-duration">15 min</div>
            </div>
        </div>

        <div class="podcast-item">
            <div class="podcast-image">
                <img src="images/podcast2.png" alt="Enjoy It! podcast">
                <button class="play-btn">
                    <div class="play-icon"></div>
                </button>
            </div>
            <div class="podcast-info">
                <div class="podcast-title">Enjoy It!</div>
                <div class="podcast-category">Socially Buzzed</div>
                <div class="podcast-duration">22 min</div>
            </div>
        </div>

        <div class="podcast-item">
            <div class="podcast-image">
                <img src="images/podcast3.png" alt="Grow with Us podcast">
                <button class="play-btn">
                    <div class="play-icon"></div>
                </button>
            </div>
            <div class="podcast-info">
                <div class="podcast-title">Grow with Us</div>
                <div class="podcast-category">Business</div>
                <div class="podcast-duration">12 min</div>
            </div>
        </div>

        <div class="podcast-item">
            <div class="podcast-image">
                <img src="images/podcast4.png" alt="Crack the Interview podcast">
                <button class="play-btn">
                    <div class="play-icon"></div>
                </button>
            </div>
            <div class="podcast-info">
                <div class="podcast-title">Crack the Interview</div>
                <div class="podcast-category">Educational</div>
                <div class="podcast-duration">30 min</div>
            </div>
        </div>
    </div>

    <div class="bottom-nav">
        <div class="nav-bar">
            <div class="nav-item active">
                <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
                    <path d="M16 3L19.09 9.26L26 10.27L21 15.14L22.18 22.02L16 18.77L9.82 22.02L11 15.14L6 10.27L12.91 9.26L16 3Z" fill="#4C0099"/>
                </svg>
            </div>
            <div class="nav-item">
                <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
                    <path d="M8 6H24C25.1 6 26 6.9 26 8V24C26 25.1 25.1 26 24 26H8C6.9 26 6 25.1 6 24V8C6 6.9 6.9 6 8 6ZM12 10V22L20 16L12 10Z" fill="#1F1F1F"/>
                </svg>
            </div>
            <div class="nav-item">
                <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
                    <path d="M16 4C9.4 4 4 9.4 4 16C4 22.6 9.4 28 16 28C22.6 28 28 22.6 28 16C28 9.4 22.6 4 16 4ZM16 10C18.2 10 20 11.8 20 14C20 16.2 18.2 18 16 18C13.8 18 12 16.2 12 14C12 11.8 13.8 10 16 10Z" fill="#1F1F1F"/>
                </svg>
            </div>
            <div class="nav-item">
                <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
                    <circle cx="16" cy="8" r="3" fill="#1F1F1F"/>
                    <circle cx="8" cy="21" r="3" fill="#1F1F1F"/>
                    <circle cx="24" cy="21" r="3" fill="#1F1F1F"/>
                </svg>
            </div>
        </div>
        <div class="nav-indicator"></div>
    </div>
</body>
</html>
