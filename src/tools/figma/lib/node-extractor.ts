/**
 * Node extraction utilities for converting hierarchical nodes to downloadable image arrays
 */

import type { SimplifiedNode, SimplifiedDesign, GlobalVars } from "./types.js";

export interface DownloadableNode {
  nodeId: string;
  fileName: string;
  imageRef?: string;
  nodeType: string;
  nodeName: string;
}

/**
 * Extract downloadable nodes from a simplified design
 */
export function extractDownloadableNodes(design: SimplifiedDesign): DownloadableNode[] {
  const downloadableNodes: DownloadableNode[] = [];
  
  function traverseNode(node: SimplifiedNode) {
    // Check if this node is downloadable
    if (isDownloadableNode(node, design.globalVars)) {
      const downloadableNode = createDownloadableNode(node, design.globalVars);
      if (downloadableNode) {
        downloadableNodes.push(downloadableNode);
      }
    }
    
    // Recursively traverse children
    if (node.children) {
      node.children.forEach(traverseNode);
    }
  }
  
  design.nodes.forEach(traverseNode);
  return downloadableNodes;
}

/**
 * Check if a node is downloadable (has images or is a vector)
 */
function isDownloadableNode(node: SimplifiedNode, globalVars: GlobalVars): boolean {
  // Vector nodes are always downloadable as SVG
  if (node.type === "VECTOR" || node.type === "IMAGE-SVG") {
    return true;
  }
  
  // Nodes with image fills are downloadable
  if (node.fills) {
    const fills = globalVars.styles[node.fills];
    if (Array.isArray(fills)) {
      return fills.some((fill: any) => fill.type === "IMAGE" && fill.imageRef);
    }
  }
  
  // Frame/Group nodes that might contain images
  if (["FRAME", "GROUP", "COMPONENT", "INSTANCE"].includes(node.type)) {
    return true;
  }
  
  return false;
}

/**
 * Create a downloadable node object
 */
function createDownloadableNode(node: SimplifiedNode, globalVars: GlobalVars): DownloadableNode | null {
  const baseNode: DownloadableNode = {
    nodeId: node.id,
    fileName: sanitizeFileName(node.name),
    nodeType: node.type,
    nodeName: node.name
  };
  
  // Check for image fills
  if (node.fills) {
    const fills = globalVars.styles[node.fills];
    if (Array.isArray(fills)) {
      const imageFill = fills.find((fill: any) => fill.type === "IMAGE" && fill.imageRef);
      if (imageFill) {
        baseNode.imageRef = imageFill.imageRef;
        baseNode.fileName = ensureExtension(baseNode.fileName, "png");
        return baseNode;
      }
    }
  }
  
  // Vector nodes default to SVG
  if (node.type === "VECTOR" || node.type === "IMAGE-SVG") {
    baseNode.fileName = ensureExtension(baseNode.fileName, "svg");
    return baseNode;
  }
  
  // Other nodes default to PNG
  if (["FRAME", "GROUP", "COMPONENT", "INSTANCE"].includes(node.type)) {
    baseNode.fileName = ensureExtension(baseNode.fileName, "png");
    return baseNode;
  }
  
  return null;
}

/**
 * Sanitize filename for safe file system usage
 */
function sanitizeFileName(name: string): string {
  return name
    .replace(/[^a-zA-Z0-9\s\-_]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .toLowerCase()
    .substring(0, 50); // Limit length
}

/**
 * Ensure filename has the correct extension
 */
function ensureExtension(fileName: string, extension: string): string {
  const hasExtension = fileName.toLowerCase().endsWith(`.${extension}`);
  return hasExtension ? fileName : `${fileName}.${extension}`;
}
